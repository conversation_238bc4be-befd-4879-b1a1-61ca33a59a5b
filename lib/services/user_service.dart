import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Map<String, Map<String, dynamic>> _userCache = {};
  final Map<String, StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>>
  _userSubscriptions = {};
  final Map<String, StreamController<Map<String, dynamic>?>> _userControllers =
      {};

  /// Get user data stream by user ID with realtime updates
  Stream<Map<String, dynamic>?> getUserDataStream(String userId) {
    if (userId.isEmpty) {
      return Stream.value(null);
    }

    // Return existing stream if already listening
    if (_userControllers.containsKey(userId)) {
      return _userControllers[userId]!.stream;
    }

    // Create new stream controller
    final controller = StreamController<Map<String, dynamic>?>.broadcast();
    _userControllers[userId] = controller;

    // Start listening to user document
    final subscription = _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .listen(
          (snapshot) {
            try {
              if (snapshot.exists) {
                final userData = snapshot.data()!;
                userData['id'] = snapshot.id;

                // Update cache
                _userCache[userId] = userData;
                controller.add(userData);
              } else {
                // User doesn't exist
                _userCache.remove(userId);
                controller.add(null);
              }
            } catch (e) {
              debugPrint('Error processing user data for $userId: $e');
              controller.addError(e);
            }
          },
          onError: (error) {
            debugPrint('Stream error for user $userId: $error');
            controller.addError(error);
          },
        );

    _userSubscriptions[userId] = subscription;
    return controller.stream;
  }

  /// Get user data by user ID with caching (legacy method for backward compatibility)
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    if (userId.isEmpty) return null;

    // Check cache first
    if (_userCache.containsKey(userId)) {
      return _userCache[userId];
    }

    // Get data from stream (this will also populate cache)
    try {
      return await getUserDataStream(
        userId,
      ).first.timeout(const Duration(seconds: 10), onTimeout: () => null);
    } catch (e) {
      debugPrint('Error fetching user data for $userId: $e');
      return null;
    }
  }

  /// Get user profile image URL with realtime updates
  Stream<String?> getUserProfileImageStream(String userId) {
    return getUserDataStream(
      userId,
    ).map((userData) => userData?['profileImageUrl'] ?? userData?['photoUrl']);
  }

  /// Get user profile image URL (legacy method)
  Future<String?> getUserProfileImage(String userId) async {
    final userData = await getUserData(userId);
    return userData?['profileImageUrl'] ?? userData?['photoUrl'];
  }

  /// Get user display name with realtime updates
  Stream<String> getUserDisplayNameStream(String userId) {
    return getUserDataStream(userId).map(
      (userData) =>
          userData?['username'] ?? userData?['name'] ?? 'Unknown User',
    );
  }

  /// Get user display name (legacy method)
  Future<String> getUserDisplayName(String userId) async {
    final userData = await getUserData(userId);
    return userData?['username'] ?? userData?['name'] ?? 'Unknown User';
  }

  /// Clear cache for a specific user (useful when user updates profile)
  void clearUserCache(String userId) {
    _userCache.remove(userId);
  }

  /// Clear all cached user data
  void clearAllCache() {
    _userCache.clear();
  }

  /// Get multiple users data efficiently with realtime updates
  Stream<Map<String, Map<String, dynamic>>> getMultipleUsersDataStream(
    List<String> userIds,
  ) {
    if (userIds.isEmpty) {
      return Stream.value({});
    }

    // Create a stream controller for the combined result
    final controller =
        StreamController<Map<String, Map<String, dynamic>>>.broadcast();
    final subscriptions = <StreamSubscription>[];
    final currentData = <String, Map<String, dynamic>?>{};

    void emitUpdate() {
      final result = <String, Map<String, dynamic>>{};
      for (final entry in currentData.entries) {
        if (entry.value != null) {
          result[entry.key] = entry.value!;
        }
      }
      controller.add(result);
    }

    // Subscribe to each user's stream
    for (final userId in userIds) {
      currentData[userId] = null;
      final subscription = getUserDataStream(userId).listen((userData) {
        currentData[userId] = userData;
        emitUpdate();
      });
      subscriptions.add(subscription);
    }

    // Clean up when stream is cancelled
    controller.onCancel = () {
      for (final subscription in subscriptions) {
        subscription.cancel();
      }
    };

    return controller.stream;
  }

  /// Get multiple users data efficiently (legacy method)
  Future<Map<String, Map<String, dynamic>>> getMultipleUsersData(
    List<String> userIds,
  ) async {
    final result = <String, Map<String, dynamic>>{};

    // Use streams to get data (this will also populate cache)
    try {
      final streamData = await getMultipleUsersDataStream(
        userIds,
      ).first.timeout(
        const Duration(seconds: 10),
        onTimeout: () => <String, Map<String, dynamic>>{},
      );
      return streamData;
    } catch (e) {
      debugPrint('Error fetching multiple users data: $e');
      return result;
    }
  }

  /// Stop listening to a specific user's updates
  void stopListeningToUser(String userId) {
    _userSubscriptions[userId]?.cancel();
    _userSubscriptions.remove(userId);
    _userControllers[userId]?.close();
    _userControllers.remove(userId);
    _userCache.remove(userId);
  }

  /// Dispose all streams and subscriptions
  void dispose() {
    for (final subscription in _userSubscriptions.values) {
      subscription.cancel();
    }
    for (final controller in _userControllers.values) {
      controller.close();
    }
    _userSubscriptions.clear();
    _userControllers.clear();
    _userCache.clear();
  }
}
