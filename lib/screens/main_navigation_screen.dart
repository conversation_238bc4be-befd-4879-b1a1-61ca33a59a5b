import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'chat_list_screen.dart';
import 'create_post.dart';
import 'search_screen.dart';
import 'profile_screen.dart';
import '../widgets/profile_drawer.dart';
import '../widgets/top_paid_post_container.dart';
import '../widgets/post_card.dart';
import '../widgets/skeleton_loader.dart';
import '../services/wallet_service.dart';
import '../services/post_service.dart';
import '../services/follow_service.dart';
import 'post_detail_screen.dart';

// Category data model
class CategoryData {
  final String name;
  final Color color;
  final double topPrice;

  const CategoryData({
    required this.name,
    required this.color,
    required this.topPrice,
  });
}

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  late List<Widget> _screens;

  // Double back to exit functionality
  DateTime? _lastBackPressed;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    final currentUid = FirebaseAuth.instance.currentUser?.uid ?? '';
    _screens = [
      const HomeScreenContent(), // Home content without bottom nav
      const ChatListScreen(),
      const CreatePostScreen(),
      const SearchScreen(),
      ProfileScreen(userId: currentUid),
    ];
  }

  Future<void> _initializeServices() async {
    try {
      await WalletService().initialize();
      await PostService().initialize();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  void _onTabTapped(int index) {
    if (_currentIndex != index) {
      HapticFeedback.selectionClick();
      setState(() {
        _currentIndex = index;
      });
    }
  }

  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    const backPressDuration = Duration(seconds: 2);

    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > backPressDuration) {
      _lastBackPressed = now;

      // Show snackbar with exit message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Press back again to exit'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false; // Don't exit
    }

    return true; // Exit the app
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldExit = await _onWillPop();
        if (shouldExit && mounted) {
          // Exit the app
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        body: AnimatedSwitcher(
          duration: const Duration(milliseconds: 250),
          transitionBuilder: (child, animation) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.05, 0),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeInOutCubic,
                ),
              ),
              child: FadeTransition(opacity: animation, child: child),
            );
          },
          child: IndexedStack(
            key: ValueKey(_currentIndex),
            index: _currentIndex,
            children: _screens,
          ),
        ),
        bottomNavigationBar: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          child: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            selectedItemColor: const Color(0xFF4C5DFF),
            unselectedItemColor: Colors.grey,
            currentIndex: _currentIndex,
            onTap: _onTabTapped,
            elevation: 8,
            backgroundColor: Colors.white,
            items: const [
              BottomNavigationBarItem(icon: Icon(Icons.home), label: ''),
              BottomNavigationBarItem(
                icon: Icon(Icons.chat_bubble_outline),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.add_box_outlined),
                label: '',
              ),
              BottomNavigationBarItem(icon: Icon(Icons.search), label: ''),
              BottomNavigationBarItem(
                icon: Icon(Icons.person_outline),
                label: '',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Extract the home screen content without the bottom navigation
class HomeScreenContent extends StatefulWidget {
  const HomeScreenContent({super.key});

  @override
  State<HomeScreenContent> createState() => _HomeScreenContentState();
}

class _HomeScreenContentState extends State<HomeScreenContent>
    with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  int currentCategoryIndex = 1; // Default to Politics (index 1)

  // Category data
  static const List<CategoryData> categories = [
    CategoryData(name: 'News', color: Color(0xFF2196F3), topPrice: 0.0),
    CategoryData(name: 'Politics', color: Color(0xFF4CAF50), topPrice: 0.0),
    CategoryData(name: 'Sports', color: Color(0xFFFF9800), topPrice: 0.0),
    CategoryData(
      name: 'Entertainment',
      color: Color(0xFFE91E63),
      topPrice: 0.0,
    ),
    CategoryData(name: 'Sex', color: Color(0xFF9C27B0), topPrice: 0.0),
    CategoryData(name: 'Religion', color: Color(0xFF795548), topPrice: 0.0),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onCategorySelected(int index) {
    setState(() {
      currentCategoryIndex = index;
    });
  }

  void resetToPolitics() {
    setState(() {
      final politicsIndex = categories.indexWhere(
        (cat) => cat.name == 'Politics',
      );
      if (politicsIndex != -1) {
        currentCategoryIndex = politicsIndex;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth >= 768;

        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.white,
          drawer: !isWideScreen ? ProfileDrawer() : null,
          appBar: HomeAppBar(scaffoldKey: _scaffoldKey),
          body: isWideScreen ? _buildWideScreenLayout() : _buildMobileLayout(),
        );
      },
    );
  }

  Widget _buildWideScreenLayout() {
    return Row(
      children: [
        // Left sidebar with profile
        Container(
          width: 300,
          decoration: BoxDecoration(
            border: Border(right: BorderSide(color: Colors.grey.shade200)),
          ),
          child: ProfileDrawer(),
        ),
        // Main content area
        Expanded(child: _buildMainContent()),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return _buildMainContent();
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        HomeTabBar(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              ExploreTab(
                currentCategoryIndex: currentCategoryIndex,
                categories: categories,
                onCategorySelected: _onCategorySelected,
              ),
              const FollowingTab(),
            ],
          ),
        ),
      ],
    );
  }
}

// Home App Bar Widget
class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;

  const HomeAppBar({super.key, required this.scaffoldKey});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.menu, color: Colors.black),
        onPressed: () {
          scaffoldKey.currentState?.openDrawer();
        },
      ),
      title: Center(
        child: Container(
          width: 40,
          height: 40,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Color(0xFF5159FF),
          ),
          child: ClipOval(
            child: Image.asset(
              'assets/images/money_mouth.png',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xFF5159FF),
                  ),
                  child: const Icon(
                    Icons.monetization_on,
                    color: Colors.white,
                    size: 24,
                  ),
                );
              },
            ),
          ),
        ),
      ),
      actions: const [
        // Empty actions - no wallet balance shown
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// Home Tab Bar Widget
class HomeTabBar extends StatelessWidget {
  final TabController tabController;

  const HomeTabBar({super.key, required this.tabController});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      // padding: const EdgeInsets.all(0),
      child: TabBar(
        controller: tabController,
        labelColor: Colors.black,
        unselectedLabelColor: Colors.grey,
        indicatorColor: const Color(0xFF4C5DFF),
        indicatorWeight: 3,
        tabs: const [Tab(text: 'Explore'), Tab(text: 'Following')],
      ),
    );
  }
}

// Explore Tab Widget
class ExploreTab extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final Function(int) onCategorySelected;

  const ExploreTab({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final PostService postService = PostService();
    final currentCategory = categories[currentCategoryIndex].name;
    final topPost = postService.getTopPaidPostForCategory(currentCategory);

    return Column(
      children: [
        HorizontalCategoriesSection(
          currentCategoryIndex: currentCategoryIndex,
          categories: categories,
          onCategorySelected: onCategorySelected,
        ),
        // Top Paid Post Container (24-hour system)
        if (topPost != null)
          TopPaidPostContainer(
            category: currentCategory,
            topPost: topPost,
            onTap: () {
              // Navigate to post detail view
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PostDetailScreen(post: topPost),
                ),
              );
            },
          ),
        Expanded(child: PostsFeed(category: currentCategory)),
      ],
    );
  }
}

// Horizontal Scrollable Categories Section Widget
class HorizontalCategoriesSection extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final Function(int) onCategorySelected;

  const HorizontalCategoriesSection({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      margin: const EdgeInsets.only(top: 4, bottom: 4),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = index == currentCategoryIndex;

          return GestureDetector(
            onTap: () {
              HapticFeedback.selectionClick();
              onCategorySelected(index);
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeInOutCubic,
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
              constraints: const BoxConstraints(minHeight: 42, maxHeight: 42),
              decoration: BoxDecoration(
                color: isSelected ? category.color : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: isSelected ? category.color : Colors.grey.shade300,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      category.name,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                    // Price display removed - will be implemented with dynamic pricing later
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Posts Feed Widget
class PostsFeed extends StatefulWidget {
  final String category;

  const PostsFeed({super.key, required this.category});

  @override
  State<PostsFeed> createState() => _PostsFeedState();
}

class _PostsFeedState extends State<PostsFeed> {
  final PostService _postService = PostService();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final posts = _postService.getPostsByCategory(widget.category);

    if (posts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.post_add, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No posts in ${widget.category}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Be the first to share something!',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/create_post');
              },
              icon: const Icon(Icons.add),
              label: const Text('Create First Post'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5159FF),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        HapticFeedback.mediumImpact();
        setState(() {});
      },
      color: const Color(0xFF4285F4),
      backgroundColor: Colors.white,
      strokeWidth: 3,
      displacement: 60,
      child: ListView.builder(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
        itemCount: posts.length,
        itemBuilder: (context, index) {
          return PostCard(
            post: posts[index],
            onLike: () => _handleLike(posts[index]),
            onPurchase: () => _handlePurchase(posts[index]),
            onView: () => _handleView(posts[index]),
            onTap: () => _handlePostTap(posts[index]),
          );
        },
      ),
    );
  }

  void _handleLike(Post post) {
    _postService.likePost(post.id);
  }

  void _handlePurchase(Post post) {
    // Handle post purchase
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Purchase functionality for ${post.formattedPrice}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleView(Post post) {
    _postService.viewPost(post.id);
  }

  void _handlePostTap(Post post) {
    // Navigate to post detail view
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }
}

// Following Tab Widget
class FollowingTab extends StatefulWidget {
  const FollowingTab({super.key});

  @override
  State<FollowingTab> createState() => _FollowingTabState();
}

class _FollowingTabState extends State<FollowingTab> {
  final FollowService _followService = FollowService();
  final PostService _postService = PostService();
  final ScrollController _scrollController = ScrollController();

  List<Post> _followingPosts = [];
  List<Post> _allFollowingPosts = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMorePosts = true;

  static const int _postsPerPage = 10;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadFollowingPosts();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMorePosts) {
        _loadMorePosts();
      }
    }
  }

  Future<void> _loadFollowingPosts() async {
    setState(() {
      _isLoading = true;
      _currentPage = 0;
      _followingPosts.clear();
      _allFollowingPosts.clear();
      _hasMorePosts = true;
    });

    try {
      // Get list of users current user is following
      final followingUserIds = await _followService.getFollowingStream().first;

      if (followingUserIds.isEmpty) {
        setState(() {
          _followingPosts = [];
          _allFollowingPosts = [];
          _isLoading = false;
        });
        return;
      }

      // Get all posts and filter by following users
      final allPosts = _postService.getAllPosts();
      final followingPosts =
          allPosts
              .where((post) => followingUserIds.contains(post.authorId))
              .toList();

      // Sort by creation date (newest first)
      followingPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _allFollowingPosts = followingPosts;
        _followingPosts = _getPaginatedPosts();
        _hasMorePosts = _followingPosts.length < _allFollowingPosts.length;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading following posts: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMorePosts() async {
    if (_isLoadingMore || !_hasMorePosts) return;

    setState(() => _isLoadingMore = true);

    // Simulate network delay for smooth UX
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _currentPage++;
      final newPosts = _getPaginatedPosts();
      _followingPosts = newPosts;
      _hasMorePosts = _followingPosts.length < _allFollowingPosts.length;
      _isLoadingMore = false;
    });
  }

  List<Post> _getPaginatedPosts() {
    final endIndex = (_currentPage + 1) * _postsPerPage;
    return _allFollowingPosts
        .take(endIndex.clamp(0, _allFollowingPosts.length))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _followingPosts.isEmpty) {
      return ListView.builder(
        padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
        itemCount: 5,
        itemBuilder: (context, index) => const PostCardSkeleton(),
      );
    }

    if (_followingPosts.isEmpty && !_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No posts from followed users',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Follow other users to see their posts here',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/connect');
              },
              icon: const Icon(Icons.person_add),
              label: const Text('Find People to Follow'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5159FF),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        HapticFeedback.mediumImpact();
        await _loadFollowingPosts();
      },
      color: const Color(0xFF4285F4),
      backgroundColor: Colors.white,
      strokeWidth: 3,
      displacement: 60,
      child: ListView.builder(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
        itemCount: _followingPosts.length + (_hasMorePosts ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at the end
          if (index == _followingPosts.length) {
            return _buildLoadingIndicator();
          }

          return PostCard(
            post: _followingPosts[index],
            onLike: () => _handleLike(_followingPosts[index]),
            onPurchase: () => _handlePurchase(_followingPosts[index]),
            onView: () => _handleView(_followingPosts[index]),
            onTap: () => _handlePostTap(_followingPosts[index]),
          );
        },
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child:
            _isLoadingMore
                ? Column(
                  key: const ValueKey('loading'),
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 1200),
                      tween: Tween(begin: 0.0, end: 1.0),
                      builder: (context, value, child) {
                        return Transform.rotate(
                          angle: value * 2 * 3.14159,
                          child: const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Color(0xFF4285F4),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    AnimatedOpacity(
                      duration: const Duration(milliseconds: 600),
                      opacity: 0.7,
                      child: const Text(
                        'Loading more posts...',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                )
                : const SizedBox.shrink(key: ValueKey('empty')),
      ),
    );
  }

  void _handleLike(Post post) {
    _postService.likePost(post.id);
  }

  void _handlePurchase(Post post) {
    // Handle purchase functionality
  }

  void _handleView(Post post) {
    _postService.viewPost(post.id);
  }

  void _handlePostTap(Post post) {
    // Navigate to post detail screen
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }
}
